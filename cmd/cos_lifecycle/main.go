package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

func main() {
	var (
		action     = flag.String("action", "", "操作类型: setup, check, list, delete, enable, disable")
		ruleID     = flag.String("rule-id", "", "规则ID")
		prefix     = flag.String("prefix", "", "对象前缀")
		days       = flag.Int("days", 0, "过期天数")
		status     = flag.String("status", "Enabled", "规则状态: Enabled/Disabled")
		configPath = flag.String("config", "", "配置文件路径")
		help       = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	if *help || *action == "" {
		printUsage()
		return
	}

	// 初始化配置
	if err := initConfig(*configPath); err != nil {
		fmt.Printf("初始化配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	logger.InitLogger(&config.GlobConfig.Logger)

	ctx := context.Background()
	manager := service.SingletonCOSLifecycleManager()

	switch *action {
	case "setup":
		if err := setupFaceFusionRule(ctx, manager); err != nil {
			fmt.Printf("设置人脸融合规则失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("成功设置人脸融合生命周期规则")

	case "check":
		if err := checkFaceFusionRule(ctx, manager); err != nil {
			fmt.Printf("检查人脸融合规则失败: %v\n", err)
			os.Exit(1)
		}

	case "list":
		if err := listAllRules(ctx, manager); err != nil {
			fmt.Printf("列出规则失败: %v\n", err)
			os.Exit(1)
		}

	case "create":
		if *ruleID == "" || *prefix == "" || *days <= 0 {
			fmt.Println("创建规则需要指定 --rule-id, --prefix 和 --days 参数")
			os.Exit(1)
		}
		if err := createCustomRule(ctx, manager, *ruleID, *prefix, *days, *status); err != nil {
			fmt.Printf("创建规则失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("成功创建规则: %s\n", *ruleID)

	case "delete":
		if *ruleID == "" {
			fmt.Println("删除规则需要指定 --rule-id 参数")
			os.Exit(1)
		}
		if err := deleteRule(ctx, manager, *ruleID); err != nil {
			fmt.Printf("删除规则失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("成功删除规则: %s\n", *ruleID)

	case "enable":
		if *ruleID == "" {
			fmt.Println("启用规则需要指定 --rule-id 参数")
			os.Exit(1)
		}
		if err := manager.EnableLifecycleRule(ctx, *ruleID); err != nil {
			fmt.Printf("启用规则失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("成功启用规则: %s\n", *ruleID)

	case "disable":
		if *ruleID == "" {
			fmt.Println("禁用规则需要指定 --rule-id 参数")
			os.Exit(1)
		}
		if err := manager.DisableLifecycleRule(ctx, *ruleID); err != nil {
			fmt.Printf("禁用规则失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("成功禁用规则: %s\n", *ruleID)

	default:
		fmt.Printf("未知操作: %s\n", *action)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("COS生命周期管理工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  cos_lifecycle --action=<操作> [选项]")
	fmt.Println()
	fmt.Println("操作:")
	fmt.Println("  setup     设置人脸融合生命周期规则")
	fmt.Println("  check     检查人脸融合生命周期规则")
	fmt.Println("  list      列出所有生命周期规则")
	fmt.Println("  create    创建自定义生命周期规则")
	fmt.Println("  delete    删除指定的生命周期规则")
	fmt.Println("  enable    启用指定的生命周期规则")
	fmt.Println("  disable   禁用指定的生命周期规则")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  --rule-id     规则ID")
	fmt.Println("  --prefix      对象前缀")
	fmt.Println("  --days        过期天数")
	fmt.Println("  --status      规则状态 (Enabled/Disabled)")
	fmt.Println("  --config      配置文件路径")
	fmt.Println("  --help        显示此帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 设置人脸融合规则")
	fmt.Println("  cos_lifecycle --action=setup")
	fmt.Println()
	fmt.Println("  # 检查人脸融合规则")
	fmt.Println("  cos_lifecycle --action=check")
	fmt.Println()
	fmt.Println("  # 列出所有规则")
	fmt.Println("  cos_lifecycle --action=list")
	fmt.Println()
	fmt.Println("  # 创建自定义规则")
	fmt.Println("  cos_lifecycle --action=create --rule-id=my-rule --prefix=temp/ --days=30")
	fmt.Println()
	fmt.Println("  # 删除规则")
	fmt.Println("  cos_lifecycle --action=delete --rule-id=my-rule")
}

func initConfig(configPath string) error {
	if configPath != "" {
		// 使用指定的配置文件
		fmt.Printf("使用配置文件: %s\n", configPath)
		// 这里需要根据实际的配置加载逻辑来实现
		// 暂时使用默认配置
	}
	// 使用默认配置初始化
	config.MustInit()
	return nil
}

func setupFaceFusionRule(ctx context.Context, manager *service.COSLifecycleManager) error {
	if config.GlobConfig.OSS.FaceFusionTTLDays <= 0 {
		fmt.Println("人脸融合TTL配置为0，跳过规则设置")
		return nil
	}

	fmt.Printf("设置人脸融合生命周期规则，TTL: %d天\n", config.GlobConfig.OSS.FaceFusionTTLDays)
	return manager.SetupFaceFusionLifecycleRule(ctx)
}

func checkFaceFusionRule(ctx context.Context, manager *service.COSLifecycleManager) error {
	status, err := manager.CheckFaceFusionLifecycleRule(ctx)
	if err != nil {
		return err
	}

	if status.Found {
		fmt.Printf("人脸融合规则状态:\n")
		fmt.Printf("  规则ID: %s\n", status.ID)
		fmt.Printf("  状态: %s\n", status.Status)
		fmt.Printf("  前缀: %s\n", status.Prefix)
		fmt.Printf("  过期天数: %d\n", status.ExpirationDays)
		fmt.Printf("  最后检查: %s\n", status.LastChecked.Format("2006-01-02 15:04:05"))

		// 检查配置一致性
		expectedTTL := config.GlobConfig.OSS.FaceFusionTTLDays
		if expectedTTL > 0 && status.ExpirationDays != expectedTTL {
			fmt.Printf("  ⚠️  配置不一致: 当前=%d天, 期望=%d天\n", status.ExpirationDays, expectedTTL)
		} else {
			fmt.Printf("  ✅ 配置一致\n")
		}
	} else {
		fmt.Println("❌ 未找到人脸融合生命周期规则")
	}

	return nil
}

func listAllRules(ctx context.Context, manager *service.COSLifecycleManager) error {
	rules, err := manager.ListLifecycleRules(ctx)
	if err != nil {
		return err
	}

	if len(rules) == 0 {
		fmt.Println("没有找到生命周期规则")
		return nil
	}

	fmt.Printf("找到 %d 个生命周期规则:\n\n", len(rules))
	for i, rule := range rules {
		fmt.Printf("%d. 规则ID: %s\n", i+1, rule.ID)
		fmt.Printf("   状态: %s\n", rule.Status)
		fmt.Printf("   前缀: %s\n", rule.Prefix)
		fmt.Printf("   过期天数: %d\n", rule.ExpirationDays)
		fmt.Printf("   最后检查: %s\n", rule.LastChecked.Format("2006-01-02 15:04:05"))
		fmt.Println()
	}

	return nil
}

func createCustomRule(ctx context.Context, manager *service.COSLifecycleManager, ruleID, prefix string, days int, status string) error {
	rule := service.LifecycleRule{
		ID:             ruleID,
		Status:         status,
		Prefix:         prefix,
		ExpirationDays: days,
		Description:    fmt.Sprintf("自定义规则: %s", ruleID),
	}

	return manager.CreateOrUpdateLifecycleRule(ctx, rule)
}

func deleteRule(ctx context.Context, manager *service.COSLifecycleManager, ruleID string) error {
	return manager.DeleteLifecycleRule(ctx, ruleID)
}
