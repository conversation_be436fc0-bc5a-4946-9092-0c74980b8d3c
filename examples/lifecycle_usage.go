package main

import (
	"context"
	"fmt"
	"log"

	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// 这是一个演示如何使用重构后的COS生命周期管理功能的示例

func main() {
	// 初始化配置
	config.MustInit()
	
	// 初始化日志
	logger.InitLogger(&config.GlobConfig.Logger)

	ctx := context.Background()

	// 示例1: 使用现有的UploadService接口（向后兼容）
	fmt.Println("=== 示例1: 使用UploadService接口 ===")
	uploadService := service.SingletonUploadService()

	// 设置人脸融合生命周期规则
	if err := uploadService.SetupCOSLifecycleRules(ctx); err != nil {
		log.Printf("设置生命周期规则失败: %v", err)
	} else {
		fmt.Println("✅ 成功设置人脸融合生命周期规则")
	}

	// 检查规则状态
	if err := uploadService.CheckCOSLifecycleRules(ctx); err != nil {
		log.Printf("检查生命周期规则失败: %v", err)
	} else {
		fmt.Println("✅ 成功检查生命周期规则")
	}

	// 示例2: 使用新的COSLifecycleManager（更多功能）
	fmt.Println("\n=== 示例2: 使用COSLifecycleManager ===")
	manager := service.SingletonCOSLifecycleManager()

	// 检查人脸融合规则状态
	status, err := manager.CheckFaceFusionLifecycleRule(ctx)
	if err != nil {
		log.Printf("检查人脸融合规则失败: %v", err)
	} else {
		if status.Found {
			fmt.Printf("✅ 人脸融合规则状态: %s, 过期天数: %d\n", status.Status, status.ExpirationDays)
		} else {
			fmt.Println("❌ 未找到人脸融合规则")
		}
	}

	// 列出所有生命周期规则
	rules, err := manager.ListLifecycleRules(ctx)
	if err != nil {
		log.Printf("列出规则失败: %v", err)
	} else {
		fmt.Printf("📋 找到 %d 个生命周期规则:\n", len(rules))
		for i, rule := range rules {
			fmt.Printf("  %d. %s (%s) - %d天\n", i+1, rule.ID, rule.Status, rule.ExpirationDays)
		}
	}

	// 示例3: 创建自定义规则
	fmt.Println("\n=== 示例3: 创建自定义规则 ===")
	
	// 创建临时文件清理规则
	tempRule := service.LifecycleRule{
		ID:             "temp-files-cleanup",
		Status:         "Enabled",
		Prefix:         "temp/",
		ExpirationDays: 7,
		Description:    "临时文件7天后自动删除",
		AbortMultipartDays: 3, // 3天后清理未完成的分片上传
	}

	if err := manager.CreateOrUpdateLifecycleRule(ctx, tempRule); err != nil {
		log.Printf("创建临时文件规则失败: %v", err)
	} else {
		fmt.Println("✅ 成功创建临时文件清理规则")
	}

	// 创建日志文件清理规则
	logRule := service.LifecycleRule{
		ID:             "log-files-cleanup",
		Status:         "Enabled",
		Prefix:         "logs/",
		ExpirationDays: 90,
		Description:    "日志文件90天后自动删除",
	}

	if err := manager.CreateOrUpdateLifecycleRule(ctx, logRule); err != nil {
		log.Printf("创建日志文件规则失败: %v", err)
	} else {
		fmt.Println("✅ 成功创建日志文件清理规则")
	}

	// 示例4: 规则管理操作
	fmt.Println("\n=== 示例4: 规则管理操作 ===")

	// 禁用临时文件规则
	if err := manager.DisableLifecycleRule(ctx, "temp-files-cleanup"); err != nil {
		log.Printf("禁用临时文件规则失败: %v", err)
	} else {
		fmt.Println("✅ 成功禁用临时文件规则")
	}

	// 重新启用临时文件规则
	if err := manager.EnableLifecycleRule(ctx, "temp-files-cleanup"); err != nil {
		log.Printf("启用临时文件规则失败: %v", err)
	} else {
		fmt.Println("✅ 成功启用临时文件规则")
	}

	// 获取特定规则状态
	tempStatus, err := manager.GetLifecycleRule(ctx, "temp-files-cleanup")
	if err != nil {
		log.Printf("获取临时文件规则状态失败: %v", err)
	} else {
		if tempStatus.Found {
			fmt.Printf("📊 临时文件规则状态: %s\n", tempStatus.Status)
		}
	}

	// 示例5: 清理演示规则（可选）
	fmt.Println("\n=== 示例5: 清理演示规则 ===")
	
	// 删除演示创建的规则
	if err := manager.DeleteLifecycleRule(ctx, "temp-files-cleanup"); err != nil {
		log.Printf("删除临时文件规则失败: %v", err)
	} else {
		fmt.Println("🗑️ 成功删除临时文件规则")
	}

	if err := manager.DeleteLifecycleRule(ctx, "log-files-cleanup"); err != nil {
		log.Printf("删除日志文件规则失败: %v", err)
	} else {
		fmt.Println("🗑️ 成功删除日志文件规则")
	}

	fmt.Println("\n🎉 演示完成！")
}

// 演示如何在应用启动时初始化生命周期规则
func initLifecycleRulesOnStartup() {
	ctx := context.Background()
	uploadService := service.SingletonUploadService()

	// 设置人脸融合生命周期规则
	if err := uploadService.SetupCOSLifecycleRules(ctx); err != nil {
		log.Printf("启动时设置生命周期规则失败: %v", err)
	}
}

// 演示如何定期检查规则状态
func periodicRuleCheck() {
	ctx := context.Background()
	uploadService := service.SingletonUploadService()

	// 检查规则状态
	if err := uploadService.CheckCOSLifecycleRules(ctx); err != nil {
		log.Printf("定期检查生命周期规则失败: %v", err)
	}
}

// 演示如何处理不同的业务场景
func businessScenarios() {
	ctx := context.Background()
	manager := service.SingletonCOSLifecycleManager()

	// 场景1: 用户上传的临时文件，24小时后删除
	tempUserFiles := service.LifecycleRule{
		ID:             "user-temp-files",
		Status:         "Enabled",
		Prefix:         "user_temp/",
		ExpirationDays: 1,
		Description:    "用户临时文件24小时后删除",
	}

	// 场景2: 系统备份文件，30天后删除
	backupFiles := service.LifecycleRule{
		ID:             "system-backup",
		Status:         "Enabled",
		Prefix:         "backup/",
		ExpirationDays: 30,
		Description:    "系统备份文件30天后删除",
	}

	// 场景3: 缓存文件，7天后删除
	cacheFiles := service.LifecycleRule{
		ID:             "cache-files",
		Status:         "Enabled",
		Prefix:         "cache/",
		ExpirationDays: 7,
		Description:    "缓存文件7天后删除",
		AbortMultipartDays: 1, // 1天后清理未完成的分片上传
	}

	rules := []service.LifecycleRule{tempUserFiles, backupFiles, cacheFiles}

	for _, rule := range rules {
		if err := manager.CreateOrUpdateLifecycleRule(ctx, rule); err != nil {
			log.Printf("创建规则 %s 失败: %v", rule.ID, err)
		} else {
			fmt.Printf("✅ 成功创建规则: %s\n", rule.ID)
		}
	}
}
