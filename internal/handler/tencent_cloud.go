package handler

import (
	"fmt"
	"strconv"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/encryption"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_tencentCloudHandler *TencentCloudHandler
)

type TencentCloudHandler struct {
	middleware.BaseHandler
	tencentCloudLogic *logic.TencentCloudLogic
}

func SingletonTencentCloudHandler() *TencentCloudHandler {
	if _tencentCloudHandler == nil {
		_tencentCloudHandler = &TencentCloudHandler{
			tencentCloudLogic: logic.SingletonTencentCloudLogic(),
		}
	}
	return _tencentCloudHandler
}

// VerifyRealName 获取实名核身结果
func (h *TencentCloudHandler) VerifyRealName(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.VerifyRealNameReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.tencentCloudLogic.VerifyRealName(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// FaceFusion 人脸融合
func (h *TencentCloudHandler) FaceFusion(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.FaceFusionReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.tencentCloudLogic.FaceFusion(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetFaceFusionImage 获取人脸融合图片（支持加密图片的代理访问）
func (h *TencentCloudHandler) GetFaceFusionImage(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取路径参数
	taskID := c.Param("task_id")
	if taskID == "" {
		h.Fail(c, fmt.Errorf("task_id is required"))
		return
	}

	// 获取图片服务
	imageService := service.SingletonFaceFusionImageService()

	var gameID, userID string
	var err error

	// 检查是否使用token验证
	token := c.Query("token")
	if token != "" {
		// 使用token验证
		var tokenTaskID string
		// 通过token验证权限，gameID/userID 仅在无token时使用，这里忽略返回的gameID/userID避免ineffassign
		tokenTaskID, _, _, err = imageService.ValidateAccessToken(token)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片] Token验证失败: task_id=%s, err=%v", taskID, err)
			h.Fail(c, fmt.Errorf("invalid or expired token"))
			return
		}

		// 验证token中的taskID与URL中的taskID是否一致
		if tokenTaskID != taskID {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片] TaskID不匹配: url_task_id=%s, token_task_id=%s", taskID, tokenTaskID)
			h.Fail(c, fmt.Errorf("task ID mismatch"))
			return
		}
	} else {
		// 使用传统的查询参数验证
		gameID = c.Query("game_id")
		userID = c.Query("user_id")

		if gameID == "" || userID == "" {
			h.Fail(c, fmt.Errorf("game_id and user_id are required"))
			return
		}

		// 验证访问权限
		if err := imageService.ValidateTaskAccess(ctx, taskID, gameID, userID); err != nil {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 访问权限验证失败: task_id=%s, err=%v", taskID, err)
			h.Fail(c, err)
			return
		}
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 请求获取图片: task_id=%s, game_id=%s, user_id=%s", taskID, gameID, userID)

	// 生成安全访问URL
	secureURL, err := imageService.GenerateSecureImageURL(ctx, taskID, gameID, userID, 60) // 默认60分钟有效期
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 生成安全URL失败: task_id=%s, err=%v", taskID, err)
		h.Fail(c, err)
		return
	}

	// 返回URL响应
	response := map[string]interface{}{
		"task_id":        taskID,
		"image_url":      secureURL,
		"expire_minutes": 60,
		"generated_at":   time.Now().Unix(),
	}

	h.Success(c, response)
	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 成功返回图片URL: task_id=%s, url=%s", taskID, secureURL)
}

// GenerateFaceFusionImageURL 生成人脸融合图片的安全访问URL
func (h *TencentCloudHandler) GenerateFaceFusionImageURL(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取路径参数
	taskID := c.Param("task_id")
	if taskID == "" {
		h.Fail(c, fmt.Errorf("task_id is required"))
		return
	}

	// 获取查询参数
	gameID := c.Query("game_id")
	userID := c.Query("user_id")
	expireMinutesStr := c.DefaultQuery("expire_minutes", "60") // 默认60分钟

	if gameID == "" || userID == "" {
		h.Fail(c, fmt.Errorf("game_id and user_id are required"))
		return
	}

	expireMinutes, err := strconv.Atoi(expireMinutesStr)
	if err != nil || expireMinutes <= 0 || expireMinutes > 1440 { // 最大24小时
		h.Fail(c, fmt.Errorf("invalid expire_minutes, must be between 1 and 1440"))
		return
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 请求生成安全URL: task_id=%s, game_id=%s, user_id=%s, expire_minutes=%d",
		taskID, gameID, userID, expireMinutes)

	// 获取图片服务
	imageService := service.SingletonFaceFusionImageService()

	// 生成安全URL
	secureURL, err := imageService.GenerateSecureImageURL(ctx, taskID, gameID, userID, expireMinutes)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 生成安全URL失败: task_id=%s, err=%v", taskID, err)
		h.Fail(c, err)
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"task_id":        taskID,
		"secure_url":     secureURL,
		"expire_minutes": expireMinutes,
		"generated_at":   time.Now().Unix(),
	}

	h.Success(c, response)
	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 成功生成安全URL: task_id=%s, url=%s", taskID, secureURL)
}

// GetFaceFusionImageContent 直接返回人脸融合结果图片（支持加密SSE-C解密代理）
// 鉴权方式：优先 token (task_id 校验)，否则使用 game_id + user_id 参数校验
// 行为：
//   - 若开启加密：后台携带 SSE-C 头从 COS 下载后以二进制流输出
//   - 若未开启加密：重定向到公开 image_url（减少带宽占用）；如果需要统一输出可改为透传
func (h *TencentCloudHandler) GetFaceFusionImageContent(c *gin.Context) {
	ctx := c.Request.Context()
	taskID := c.Param("task_id")
	if taskID == "" {
		h.Fail(c, fmt.Errorf("task_id is required"))
		return
	}

	imageService := service.SingletonFaceFusionImageService()

	var gameID, userID string
	var err error

	// 1. 鉴权：token 优先
	token := c.Query("token")
	if token != "" {
		var tokenTaskID string
		// token 模式下仅需验证taskID与过期时间，gameID/userID可在后续统计需要时再扩展
		tokenTaskID, _, _, err = imageService.ValidateAccessToken(token)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片直出] Token验证失败: task_id=%s, err=%v", taskID, err)
			h.Fail(c, fmt.Errorf("invalid or expired token"))
			return
		}
		if tokenTaskID != taskID {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片直出] TaskID不匹配: url_task_id=%s, token_task_id=%s", taskID, tokenTaskID)
			h.Fail(c, fmt.Errorf("task ID mismatch"))
			return
		}
	} else {
		// 回退到 game_id + user_id 校验
		gameID = c.Query("game_id")
		userID = c.Query("user_id")
		if gameID == "" || userID == "" {
			h.Fail(c, fmt.Errorf("game_id and user_id are required"))
			return
		}
		if err := imageService.ValidateTaskAccess(ctx, taskID, gameID, userID); err != nil {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片直出] 访问权限验证失败: task_id=%s, err=%v", taskID, err)
			h.Fail(c, err)
			return
		}
	}

	// 2. 判断是否启用加密
	if !encryption.IsFaceFusionEncryptionEnabled() {
		// 未加密：重定向到公开 URL
		faceFusionService := service.SingletonFaceFusionService()
		record, recErr := faceFusionService.GetFaceFusionByTaskID(ctx, taskID)
		if recErr != nil || record == nil {
			logger.Logger.WarnfCtx(ctx, "[人脸融合图片直出] 记录不存在: task_id=%s, err=%v", taskID, recErr)
			h.Fail(c, fmt.Errorf("task not found"))
			return
		}
		if record.Status != "success" || record.ImageURL == "" {
			h.Fail(c, fmt.Errorf("task not completed"))
			return
		}
		logger.Logger.InfofCtx(ctx, "[人脸融合图片直出] 非加密模式重定向: task_id=%s", taskID)
		c.Redirect(302, record.ImageURL)
		return
	}

	// 3. 加密模式：下载并输出
	data, contentType, dErr := imageService.GetEncryptedImage(ctx, taskID)
	if dErr != nil {
		h.Fail(c, dErr)
		return
	}
	if contentType == "" {
		contentType = "image/jpeg"
	}
	c.Header("Cache-Control", "private, max-age=3600")
	logger.Logger.InfofCtx(ctx, "[人脸融合图片直出] 输出加密解密后图片: task_id=%s, size=%d", taskID, len(data))
	c.Data(200, contentType, data)
}
