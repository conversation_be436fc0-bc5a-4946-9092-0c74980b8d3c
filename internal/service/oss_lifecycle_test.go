package service

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// TestCOSLifecycleManager 测试COS生命周期管理器
func TestCOSLifecycleManager(t *testing.T) {
	// 初始化配置（测试环境）
	initTestConfig()

	ctx := context.Background()
	manager := SingletonCOSLifecycleManager()

	t.Run("测试配置验证", func(t *testing.T) {
		// 测试无效配置
		originalConfig := manager.config
		manager.config = &config.OSS{} // 空配置

		err := manager.validateConfig()
		if err == nil {
			t.Error("期望配置验证失败，但没有返回错误")
		}

		// 恢复配置
		manager.config = originalConfig
	})

	t.Run("测试规则验证", func(t *testing.T) {
		// 测试有效规则
		validRule := LifecycleRule{
			ID:             "test-rule",
			Status:         "Enabled",
			Prefix:         "test/",
			ExpirationDays: 30,
		}
		err := manager.validateRule(validRule)
		if err != nil {
			t.Errorf("有效规则验证失败: %v", err)
		}

		// 测试无效规则
		invalidRule := LifecycleRule{
			ID:             "", // 空ID
			Status:         "Enabled",
			ExpirationDays: 30,
		}
		err = manager.validateRule(invalidRule)
		if err == nil {
			t.Error("期望无效规则验证失败，但没有返回错误")
		}
	})

	t.Run("测试人脸融合规则设置", func(t *testing.T) {
		// 设置TTL为7天
		originalTTL := config.GlobConfig.OSS.FaceFusionTTLDays
		config.GlobConfig.OSS.FaceFusionTTLDays = 7
		defer func() {
			config.GlobConfig.OSS.FaceFusionTTLDays = originalTTL
		}()

		// 测试设置规则（在实际环境中可能会失败，这是正常的）
		err := manager.SetupFaceFusionLifecycleRule(ctx)
		if err != nil {
			t.Logf("设置人脸融合规则失败（测试环境预期）: %v", err)
		} else {
			t.Log("成功设置人脸融合规则")
		}
	})

	t.Run("测试UploadService集成", func(t *testing.T) {
		uploadService := SingletonUploadService()

		// 测试设置规则
		err := uploadService.SetupCOSLifecycleRules(ctx)
		if err != nil {
			t.Logf("UploadService设置规则失败（测试环境预期）: %v", err)
		}

		// 测试检查规则
		err = uploadService.CheckCOSLifecycleRules(ctx)
		if err != nil {
			t.Logf("UploadService检查规则失败（测试环境预期）: %v", err)
		}
	})
}

// TestLifecycleRuleOperations 测试生命周期规则操作
func TestLifecycleRuleOperations(t *testing.T) {
	initTestConfig()

	manager := SingletonCOSLifecycleManager()

	// 测试规则构建
	t.Run("测试规则构建", func(t *testing.T) {
		rule := LifecycleRule{
			ID:                 "test-build-rule",
			Status:             "Enabled",
			Prefix:             "test/build/",
			ExpirationDays:     15,
			AbortMultipartDays: 7,
		}

		cosRule := manager.buildCOSRule(rule)

		if cosRule.ID != rule.ID {
			t.Errorf("规则ID不匹配: 期望=%s, 实际=%s", rule.ID, cosRule.ID)
		}
		if cosRule.Status != rule.Status {
			t.Errorf("规则状态不匹配: 期望=%s, 实际=%s", rule.Status, cosRule.Status)
		}
		if cosRule.Filter.Prefix != rule.Prefix {
			t.Errorf("规则前缀不匹配: 期望=%s, 实际=%s", rule.Prefix, cosRule.Filter.Prefix)
		}
		if cosRule.Expiration.Days != rule.ExpirationDays {
			t.Errorf("过期天数不匹配: 期望=%d, 实际=%d", rule.ExpirationDays, cosRule.Expiration.Days)
		}
		if cosRule.AbortIncompleteMultipartUpload == nil {
			t.Error("未设置分片上传清理规则")
		} else if cosRule.AbortIncompleteMultipartUpload.DaysAfterInitiation != rule.AbortMultipartDays {
			t.Errorf("分片上传清理天数不匹配: 期望=%d, 实际=%d",
				rule.AbortMultipartDays, cosRule.AbortIncompleteMultipartUpload.DaysAfterInitiation)
		}
	})
}

// initTestConfig 初始化测试配置
func initTestConfig() {
	// 设置基本的测试配置
	if config.GlobConfig.OSS.BucketURL == "" {
		config.GlobConfig.OSS = config.OSS{
			Env:               "test",
			BucketURL:         "https://test-bucket.cos.ap-beijing.myqcloud.com",
			SecretID:          "test-secret-id",
			SecretKey:         "test-secret-key",
			FaceFusionTTLDays: 7,
		}
	}

	// 初始化日志（如果还没有初始化）
	if logger.Logger == nil {
		logger.InitLogger(&config.LoggerConf{
			Level: "debug",
		})
	}
}

// BenchmarkLifecycleRuleValidation 基准测试规则验证性能
func BenchmarkLifecycleRuleValidation(b *testing.B) {
	manager := SingletonCOSLifecycleManager()
	rule := LifecycleRule{
		ID:             "benchmark-rule",
		Status:         "Enabled",
		Prefix:         "benchmark/",
		ExpirationDays: 30,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = manager.validateRule(rule)
	}
}

// TestLifecycleRuleStatus 测试生命周期规则状态结构
func TestLifecycleRuleStatus(t *testing.T) {
	status := LifecycleRuleStatus{
		ID:             "test-status",
		Status:         "Enabled",
		Prefix:         "test/",
		ExpirationDays: 30,
		Found:          true,
		LastChecked:    time.Now(),
	}

	if status.ID == "" {
		t.Error("规则ID不应为空")
	}
	if !status.Found {
		t.Error("规则应该被标记为找到")
	}
	if status.LastChecked.IsZero() {
		t.Error("最后检查时间不应为零值")
	}
}
