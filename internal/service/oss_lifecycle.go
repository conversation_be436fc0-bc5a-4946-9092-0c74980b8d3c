package service

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// SetupCOSLifecycleRules 设置COS生命周期规则（简化版本）
func (s *UploadService) SetupCOSLifecycleRules(ctx context.Context) error {
	// 检查TTL配置
	if config.GlobConfig.OSS.FaceFusionTTLDays <= 0 {
		logger.Logger.InfofCtx(ctx, "[OSS生命周期] TTL配置为0，跳过生命周期规则设置")
		return nil
	}

	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 开始设置人脸融合生命周期规则，TTL: %d天",
		config.GlobConfig.OSS.FaceFusionTTLDays)

	// 创建COS客户端
	client, err := s.createCOSClient()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 创建COS客户端失败: %v", err)
		return fmt.Errorf("创建COS客户端失败: %w", err)
	}

	// 获取现有规则
	existingRules, err := s.getExistingRules(ctx, client)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 获取现有规则失败: %v", err)
		return fmt.Errorf("获取现有规则失败: %w", err)
	}

	// 构建人脸融合规则
	faceFusionRule := cos.BucketLifecycleRule{
		ID:     "face-fusion-auto-delete",
		Status: "Enabled",
		Filter: &cos.BucketLifecycleFilter{
			Prefix: fmt.Sprintf("%s/face_fusion/", config.GlobConfig.OSS.Env),
		},
		Expiration: &cos.BucketLifecycleExpiration{
			Days: config.GlobConfig.OSS.FaceFusionTTLDays,
		},
		// 添加未完成分片上传清理规则
		AbortIncompleteMultipartUpload: &cos.BucketLifecycleAbortIncompleteMultipartUpload{
			DaysAfterInitiation: 7, // 7天后清理未完成的分片上传
		},
	}

	// 更新或添加规则
	newRules := s.updateRuleList(existingRules, faceFusionRule)

	// 应用规则
	opt := &cos.BucketPutLifecycleOptions{
		Rules: newRules,
	}

	_, err = client.Bucket.PutLifecycle(ctx, opt)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 设置生命周期规则失败: %v", err)
		return fmt.Errorf("设置COS生命周期规则失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 成功设置生命周期规则: %d天后自动删除face_fusion目录下的文件",
		config.GlobConfig.OSS.FaceFusionTTLDays)

	return nil
}

// CheckCOSLifecycleRules 检查COS生命周期规则（简化版本）
func (s *UploadService) CheckCOSLifecycleRules(ctx context.Context) error {
	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 开始检查人脸融合生命周期规则")

	// 创建COS客户端
	client, err := s.createCOSClient()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 创建COS客户端失败: %v", err)
		return fmt.Errorf("创建COS客户端失败: %w", err)
	}

	// 获取生命周期规则
	result, _, err := client.Bucket.GetLifecycle(ctx)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 获取生命周期规则失败: %v", err)
		return fmt.Errorf("获取COS生命周期规则失败: %w", err)
	}

	// 检查是否存在人脸融合相关规则
	faceFusionRuleFound := false
	for _, rule := range result.Rules {
		if rule.ID == "face-fusion-auto-delete" {
			faceFusionRuleFound = true
			logger.Logger.InfofCtx(ctx, "[OSS生命周期] 找到人脸融合自动删除规则: 状态=%s, 前缀=%s, 天数=%d",
				rule.Status, rule.Filter.Prefix, rule.Expiration.Days)

			// 检查配置是否一致
			expectedTTL := config.GlobConfig.OSS.FaceFusionTTLDays
			if expectedTTL > 0 && rule.Expiration.Days != expectedTTL {
				logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 规则配置不一致: 当前=%d天, 期望=%d天, 建议更新规则",
					rule.Expiration.Days, expectedTTL)
			}
			break
		}
	}

	if !faceFusionRuleFound {
		logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 未找到人脸融合自动删除规则，建议设置生命周期规则")
	}

	return nil
}

// createCOSClient 创建COS客户端
func (s *UploadService) createCOSClient() (*cos.Client, error) {
	u, err := url.Parse(config.GlobConfig.OSS.BucketURL)
	if err != nil {
		return nil, fmt.Errorf("解析Bucket URL失败: %w", err)
	}

	baseURL := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(baseURL, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	return client, nil
}

// getExistingRules 获取现有的生命周期规则
func (s *UploadService) getExistingRules(ctx context.Context, client *cos.Client) ([]cos.BucketLifecycleRule, error) {
	result, _, err := client.Bucket.GetLifecycle(ctx)
	if err != nil {
		// 如果没有配置生命周期规则，返回空列表而不是错误
		if strings.Contains(err.Error(), "NoSuchLifecycleConfiguration") {
			return []cos.BucketLifecycleRule{}, nil
		}
		return nil, err
	}
	return result.Rules, nil
}

// updateRuleList 更新规则列表，如果规则存在则更新，否则添加
func (s *UploadService) updateRuleList(existingRules []cos.BucketLifecycleRule, newRule cos.BucketLifecycleRule) []cos.BucketLifecycleRule {
	var updatedRules []cos.BucketLifecycleRule
	ruleUpdated := false

	// 更新现有规则或保留其他规则
	for _, rule := range existingRules {
		if rule.ID == newRule.ID {
			// 更新现有规则
			updatedRules = append(updatedRules, newRule)
			ruleUpdated = true
		} else {
			// 保留其他规则
			updatedRules = append(updatedRules, rule)
		}
	}

	// 如果规则不存在，添加新规则
	if !ruleUpdated {
		updatedRules = append(updatedRules, newRule)
	}

	return updatedRules
}
