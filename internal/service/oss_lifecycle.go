package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// COSLifecycleManager COS生命周期管理器
type COSLifecycleManager struct {
	client *cos.Client
	config *config.OSS
	mu     sync.RWMutex
}

// LifecycleRule 生命周期规则配置
type LifecycleRule struct {
	ID                 string // 规则ID
	Status             string // 规则状态：Enabled/Disabled
	Prefix             string // 对象前缀
	ExpirationDays     int    // 过期天数
	Description        string // 规则描述
	AbortMultipartDays int    // 未完成分片上传清理天数（可选）
}

// LifecycleRuleStatus 生命周期规则状态
type LifecycleRuleStatus struct {
	ID             string    `json:"id"`
	Status         string    `json:"status"`
	Prefix         string    `json:"prefix"`
	ExpirationDays int       `json:"expiration_days"`
	Found          bool      `json:"found"`
	LastChecked    time.Time `json:"last_checked"`
}

var (
	_lifecycleManagerOnce sync.Once
	_lifecycleManager     *COSLifecycleManager
)

// SingletonCOSLifecycleManager 获取COS生命周期管理器单例
func SingletonCOSLifecycleManager() *COSLifecycleManager {
	_lifecycleManagerOnce.Do(func() {
		_lifecycleManager = &COSLifecycleManager{
			config: &config.GlobConfig.OSS,
		}
	})
	return _lifecycleManager
}

// initClient 初始化COS客户端
func (m *COSLifecycleManager) initClient() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.client != nil {
		return nil
	}

	// 验证配置
	if err := m.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 解析Bucket URL
	u, err := url.Parse(m.config.BucketURL)
	if err != nil {
		return fmt.Errorf("解析Bucket URL失败: %w", err)
	}

	// 创建COS客户端
	baseURL := &cos.BaseURL{BucketURL: u}
	m.client = cos.NewClient(baseURL, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  m.config.SecretID,
			SecretKey: m.config.SecretKey,
		},
		Timeout: 30 * time.Second, // 设置超时时间
	})

	return nil
}

// validateConfig 验证配置参数
func (m *COSLifecycleManager) validateConfig() error {
	if m.config.BucketURL == "" {
		return errors.New("BucketURL不能为空")
	}
	if m.config.SecretID == "" {
		return errors.New("SecretID不能为空")
	}
	if m.config.SecretKey == "" {
		return errors.New("SecretKey不能为空")
	}
	if m.config.Env == "" {
		return errors.New("环境标识(Env)不能为空")
	}
	return nil
}

// getClient 获取COS客户端（线程安全）
func (m *COSLifecycleManager) getClient() (*cos.Client, error) {
	m.mu.RLock()
	client := m.client
	m.mu.RUnlock()

	if client == nil {
		if err := m.initClient(); err != nil {
			return nil, err
		}
		m.mu.RLock()
		client = m.client
		m.mu.RUnlock()
	}

	return client, nil
}

// CreateOrUpdateLifecycleRule 创建或更新生命周期规则
func (m *COSLifecycleManager) CreateOrUpdateLifecycleRule(ctx context.Context, rule LifecycleRule) error {
	client, err := m.getClient()
	if err != nil {
		return fmt.Errorf("获取COS客户端失败: %w", err)
	}

	// 验证规则参数
	if err := m.validateRule(rule); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	// 获取现有规则
	existingRules, err := m.getAllLifecycleRules(ctx, client)
	if err != nil {
		return fmt.Errorf("获取现有规则失败: %w", err)
	}

	// 构建新的规则列表
	var newRules []cos.BucketLifecycleRule
	ruleUpdated := false

	// 更新现有规则或添加新规则
	for _, existingRule := range existingRules {
		if existingRule.ID == rule.ID {
			// 更新现有规则
			newRules = append(newRules, m.buildCOSRule(rule))
			ruleUpdated = true
		} else {
			// 保留其他规则
			newRules = append(newRules, existingRule)
		}
	}

	// 如果规则不存在，添加新规则
	if !ruleUpdated {
		newRules = append(newRules, m.buildCOSRule(rule))
	}

	// 应用规则
	opt := &cos.BucketPutLifecycleOptions{
		Rules: newRules,
	}

	_, err = client.Bucket.PutLifecycle(ctx, opt)
	if err != nil {
		return fmt.Errorf("应用生命周期规则失败: %w", err)
	}

	return nil
}

// validateRule 验证生命周期规则
func (m *COSLifecycleManager) validateRule(rule LifecycleRule) error {
	if strings.TrimSpace(rule.ID) == "" {
		return errors.New("规则ID不能为空")
	}
	if rule.Status != "Enabled" && rule.Status != "Disabled" {
		return errors.New("规则状态必须为Enabled或Disabled")
	}
	if rule.ExpirationDays <= 0 {
		return errors.New("过期天数必须大于0")
	}
	if rule.ExpirationDays > 3650 { // 限制最大10年
		return errors.New("过期天数不能超过3650天")
	}
	return nil
}

// buildCOSRule 构建COS生命周期规则
func (m *COSLifecycleManager) buildCOSRule(rule LifecycleRule) cos.BucketLifecycleRule {
	cosRule := cos.BucketLifecycleRule{
		ID:     rule.ID,
		Status: rule.Status,
		Filter: &cos.BucketLifecycleFilter{
			Prefix: rule.Prefix,
		},
		Expiration: &cos.BucketLifecycleExpiration{
			Days: rule.ExpirationDays,
		},
	}

	// 如果配置了未完成分片上传清理天数，添加相应规则
	if rule.AbortMultipartDays > 0 {
		cosRule.AbortIncompleteMultipartUpload = &cos.BucketLifecycleAbortIncompleteMultipartUpload{
			DaysAfterInitiation: rule.AbortMultipartDays,
		}
	}

	return cosRule
}

// getAllLifecycleRules 获取所有生命周期规则
func (m *COSLifecycleManager) getAllLifecycleRules(ctx context.Context, client *cos.Client) ([]cos.BucketLifecycleRule, error) {
	result, _, err := client.Bucket.GetLifecycle(ctx)
	if err != nil {
		// 如果没有配置生命周期规则，返回空列表而不是错误
		if strings.Contains(err.Error(), "NoSuchLifecycleConfiguration") {
			return []cos.BucketLifecycleRule{}, nil
		}
		return nil, err
	}
	return result.Rules, nil
}

// GetLifecycleRule 获取指定ID的生命周期规则
func (m *COSLifecycleManager) GetLifecycleRule(ctx context.Context, ruleID string) (*LifecycleRuleStatus, error) {
	client, err := m.getClient()
	if err != nil {
		return nil, fmt.Errorf("获取COS客户端失败: %w", err)
	}

	rules, err := m.getAllLifecycleRules(ctx, client)
	if err != nil {
		return nil, fmt.Errorf("获取生命周期规则失败: %w", err)
	}

	// 查找指定规则
	for _, rule := range rules {
		if rule.ID == ruleID {
			status := &LifecycleRuleStatus{
				ID:             rule.ID,
				Status:         rule.Status,
				Prefix:         rule.Filter.Prefix,
				ExpirationDays: rule.Expiration.Days,
				Found:          true,
				LastChecked:    time.Now(),
			}
			return status, nil
		}
	}

	// 规则不存在
	return &LifecycleRuleStatus{
		ID:          ruleID,
		Found:       false,
		LastChecked: time.Now(),
	}, nil
}

// ListLifecycleRules 列出所有生命周期规则
func (m *COSLifecycleManager) ListLifecycleRules(ctx context.Context) ([]LifecycleRuleStatus, error) {
	client, err := m.getClient()
	if err != nil {
		return nil, fmt.Errorf("获取COS客户端失败: %w", err)
	}

	rules, err := m.getAllLifecycleRules(ctx, client)
	if err != nil {
		return nil, fmt.Errorf("获取生命周期规则失败: %w", err)
	}

	var result []LifecycleRuleStatus
	for _, rule := range rules {
		status := LifecycleRuleStatus{
			ID:             rule.ID,
			Status:         rule.Status,
			Prefix:         rule.Filter.Prefix,
			ExpirationDays: rule.Expiration.Days,
			Found:          true,
			LastChecked:    time.Now(),
		}
		result = append(result, status)
	}

	return result, nil
}

// DeleteLifecycleRule 删除指定的生命周期规则
func (m *COSLifecycleManager) DeleteLifecycleRule(ctx context.Context, ruleID string) error {
	client, err := m.getClient()
	if err != nil {
		return fmt.Errorf("获取COS客户端失败: %w", err)
	}

	// 获取现有规则
	existingRules, err := m.getAllLifecycleRules(ctx, client)
	if err != nil {
		return fmt.Errorf("获取现有规则失败: %w", err)
	}

	// 构建新的规则列表（排除要删除的规则）
	var newRules []cos.BucketLifecycleRule
	ruleFound := false

	for _, rule := range existingRules {
		if rule.ID != ruleID {
			newRules = append(newRules, rule)
		} else {
			ruleFound = true
		}
	}

	if !ruleFound {
		return fmt.Errorf("规则ID '%s' 不存在", ruleID)
	}

	// 如果没有剩余规则，删除整个生命周期配置
	if len(newRules) == 0 {
		_, err = client.Bucket.DeleteLifecycle(ctx)
		if err != nil {
			return fmt.Errorf("删除生命周期配置失败: %w", err)
		}
	} else {
		// 应用新的规则列表
		opt := &cos.BucketPutLifecycleOptions{
			Rules: newRules,
		}
		_, err = client.Bucket.PutLifecycle(ctx, opt)
		if err != nil {
			return fmt.Errorf("更新生命周期规则失败: %w", err)
		}
	}

	return nil
}

// EnableLifecycleRule 启用指定的生命周期规则
func (m *COSLifecycleManager) EnableLifecycleRule(ctx context.Context, ruleID string) error {
	return m.updateRuleStatus(ctx, ruleID, "Enabled")
}

// DisableLifecycleRule 禁用指定的生命周期规则
func (m *COSLifecycleManager) DisableLifecycleRule(ctx context.Context, ruleID string) error {
	return m.updateRuleStatus(ctx, ruleID, "Disabled")
}

// updateRuleStatus 更新规则状态
func (m *COSLifecycleManager) updateRuleStatus(ctx context.Context, ruleID, status string) error {
	client, err := m.getClient()
	if err != nil {
		return fmt.Errorf("获取COS客户端失败: %w", err)
	}

	// 获取现有规则
	existingRules, err := m.getAllLifecycleRules(ctx, client)
	if err != nil {
		return fmt.Errorf("获取现有规则失败: %w", err)
	}

	// 更新指定规则的状态
	var newRules []cos.BucketLifecycleRule
	ruleFound := false

	for _, rule := range existingRules {
		if rule.ID == ruleID {
			rule.Status = status
			ruleFound = true
		}
		newRules = append(newRules, rule)
	}

	if !ruleFound {
		return fmt.Errorf("规则ID '%s' 不存在", ruleID)
	}

	// 应用更新后的规则
	opt := &cos.BucketPutLifecycleOptions{
		Rules: newRules,
	}

	_, err = client.Bucket.PutLifecycle(ctx, opt)
	if err != nil {
		return fmt.Errorf("更新规则状态失败: %w", err)
	}

	return nil
}

// SetupFaceFusionLifecycleRule 设置人脸融合生命周期规则
func (m *COSLifecycleManager) SetupFaceFusionLifecycleRule(ctx context.Context) error {
	// 检查TTL配置
	if m.config.FaceFusionTTLDays <= 0 {
		return nil // TTL为0表示不设置生命周期规则
	}

	// 构建人脸融合规则
	rule := LifecycleRule{
		ID:                 "face-fusion-auto-delete",
		Status:             "Enabled",
		Prefix:             fmt.Sprintf("%s/face_fusion/", m.config.Env),
		ExpirationDays:     m.config.FaceFusionTTLDays,
		Description:        "人脸融合图片自动删除规则",
		AbortMultipartDays: 7, // 7天后清理未完成的分片上传
	}

	return m.CreateOrUpdateLifecycleRule(ctx, rule)
}

// CheckFaceFusionLifecycleRule 检查人脸融合生命周期规则
func (m *COSLifecycleManager) CheckFaceFusionLifecycleRule(ctx context.Context) (*LifecycleRuleStatus, error) {
	return m.GetLifecycleRule(ctx, "face-fusion-auto-delete")
}

// ===== 与现有UploadService的集成方法 =====

// SetupCOSLifecycleRules 设置COS生命周期规则（兼容现有接口）
func (s *UploadService) SetupCOSLifecycleRules(ctx context.Context) error {
	manager := SingletonCOSLifecycleManager()

	if config.GlobConfig.OSS.FaceFusionTTLDays <= 0 {
		logger.Logger.InfofCtx(ctx, "[OSS生命周期] TTL配置为0，跳过生命周期规则设置")
		return nil
	}

	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 开始设置人脸融合生命周期规则，TTL: %d天",
		config.GlobConfig.OSS.FaceFusionTTLDays)

	err := manager.SetupFaceFusionLifecycleRule(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 设置生命周期规则失败: %v", err)
		return fmt.Errorf("设置COS生命周期规则失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 成功设置生命周期规则: %d天后自动删除face_fusion目录下的文件",
		config.GlobConfig.OSS.FaceFusionTTLDays)

	return nil
}

// CheckCOSLifecycleRules 检查COS生命周期规则（兼容现有接口）
func (s *UploadService) CheckCOSLifecycleRules(ctx context.Context) error {
	manager := SingletonCOSLifecycleManager()

	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 开始检查人脸融合生命周期规则")

	status, err := manager.CheckFaceFusionLifecycleRule(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 检查生命周期规则失败: %v", err)
		return fmt.Errorf("检查COS生命周期规则失败: %w", err)
	}

	if status.Found {
		logger.Logger.InfofCtx(ctx, "[OSS生命周期] 找到人脸融合自动删除规则: 状态=%s, 前缀=%s, 天数=%d",
			status.Status, status.Prefix, status.ExpirationDays)

		// 检查配置是否一致
		expectedTTL := config.GlobConfig.OSS.FaceFusionTTLDays
		if expectedTTL > 0 && status.ExpirationDays != expectedTTL {
			logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 规则配置不一致: 当前=%d天, 期望=%d天, 建议更新规则",
				status.ExpirationDays, expectedTTL)
		}
	} else {
		logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 未找到人脸融合自动删除规则，建议设置生命周期规则")
	}

	return nil
}

// GetLifecycleRuleStatus 获取生命周期规则状态（新增方法）
func (s *UploadService) GetLifecycleRuleStatus(ctx context.Context, ruleID string) (*LifecycleRuleStatus, error) {
	manager := SingletonCOSLifecycleManager()
	return manager.GetLifecycleRule(ctx, ruleID)
}

// ListAllLifecycleRules 列出所有生命周期规则（新增方法）
func (s *UploadService) ListAllLifecycleRules(ctx context.Context) ([]LifecycleRuleStatus, error) {
	manager := SingletonCOSLifecycleManager()
	return manager.ListLifecycleRules(ctx)
}
