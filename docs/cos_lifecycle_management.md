# COS生命周期管理重构说明

## 📋 概述

本文档描述了对 `internal/service/oss_lifecycle.go` 文件的完整重构，提供了更加健壮、灵活和易于维护的腾讯云COS生命周期管理功能。

## ✨ 重构亮点

### 1. 架构改进
- **单例模式**: 使用 `COSLifecycleManager` 单例管理器，避免重复创建客户端
- **线程安全**: 使用读写锁确保并发安全
- **配置验证**: 完整的配置参数验证机制
- **错误处理**: 详细的错误信息和适当的错误包装

### 2. 功能完善
- **CRUD操作**: 支持创建、读取、更新、删除生命周期规则
- **规则管理**: 支持启用/禁用规则
- **状态检查**: 详细的规则状态信息
- **配置一致性**: 自动检查配置与实际规则的一致性

### 3. 代码质量
- **类型安全**: 定义了明确的数据结构
- **文档完整**: 详细的注释和文档
- **测试覆盖**: 完整的单元测试
- **最佳实践**: 遵循腾讯云COS SDK最佳实践

## 🏗️ 架构设计

### 核心组件

```go
// COSLifecycleManager - 核心管理器
type COSLifecycleManager struct {
    client *cos.Client      // COS客户端
    config *config.OSS      // OSS配置
    mu     sync.RWMutex     // 读写锁
}

// LifecycleRule - 生命周期规则配置
type LifecycleRule struct {
    ID                 string // 规则ID
    Status             string // 规则状态：Enabled/Disabled
    Prefix             string // 对象前缀
    ExpirationDays     int    // 过期天数
    Description        string // 规则描述
    AbortMultipartDays int    // 未完成分片上传清理天数
}

// LifecycleRuleStatus - 规则状态信息
type LifecycleRuleStatus struct {
    ID             string    `json:"id"`
    Status         string    `json:"status"`
    Prefix         string    `json:"prefix"`
    ExpirationDays int       `json:"expiration_days"`
    Found          bool      `json:"found"`
    LastChecked    time.Time `json:"last_checked"`
}
```

### 主要方法

#### 管理器方法
- `SingletonCOSLifecycleManager()` - 获取单例管理器
- `CreateOrUpdateLifecycleRule()` - 创建或更新规则
- `GetLifecycleRule()` - 获取指定规则
- `ListLifecycleRules()` - 列出所有规则
- `DeleteLifecycleRule()` - 删除规则
- `EnableLifecycleRule()` - 启用规则
- `DisableLifecycleRule()` - 禁用规则

#### 人脸融合专用方法
- `SetupFaceFusionLifecycleRule()` - 设置人脸融合规则
- `CheckFaceFusionLifecycleRule()` - 检查人脸融合规则

#### UploadService集成方法
- `SetupCOSLifecycleRules()` - 兼容现有接口
- `CheckCOSLifecycleRules()` - 兼容现有接口
- `GetLifecycleRuleStatus()` - 新增方法
- `ListAllLifecycleRules()` - 新增方法

## 🚀 使用方法

### 1. 基本使用

```go
ctx := context.Background()
manager := service.SingletonCOSLifecycleManager()

// 设置人脸融合生命周期规则
err := manager.SetupFaceFusionLifecycleRule(ctx)
if err != nil {
    log.Printf("设置规则失败: %v", err)
}

// 检查规则状态
status, err := manager.CheckFaceFusionLifecycleRule(ctx)
if err != nil {
    log.Printf("检查规则失败: %v", err)
}

if status.Found {
    log.Printf("规则状态: %s, 过期天数: %d", status.Status, status.ExpirationDays)
}
```

### 2. 自定义规则管理

```go
// 创建自定义规则
rule := service.LifecycleRule{
    ID:             "temp-files-cleanup",
    Status:         "Enabled",
    Prefix:         "temp/",
    ExpirationDays: 7,
    Description:    "临时文件清理规则",
    AbortMultipartDays: 3,
}

err := manager.CreateOrUpdateLifecycleRule(ctx, rule)
if err != nil {
    log.Printf("创建规则失败: %v", err)
}

// 列出所有规则
rules, err := manager.ListLifecycleRules(ctx)
if err != nil {
    log.Printf("列出规则失败: %v", err)
}

for _, rule := range rules {
    log.Printf("规则: %s, 状态: %s", rule.ID, rule.Status)
}
```

### 3. 与现有代码集成

```go
// 使用现有的UploadService接口
uploadService := service.SingletonUploadService()

// 设置生命周期规则
err := uploadService.SetupCOSLifecycleRules(ctx)
if err != nil {
    log.Printf("设置失败: %v", err)
}

// 检查规则
err = uploadService.CheckCOSLifecycleRules(ctx)
if err != nil {
    log.Printf("检查失败: %v", err)
}
```

## 🛠️ 命令行工具

提供了 `cmd/cos_lifecycle/main.go` 命令行工具用于管理生命周期规则：

### 编译和使用

```bash
# 编译
go build -o cos_lifecycle cmd/cos_lifecycle/main.go

# 设置人脸融合规则
./cos_lifecycle --action=setup

# 检查人脸融合规则
./cos_lifecycle --action=check

# 列出所有规则
./cos_lifecycle --action=list

# 创建自定义规则
./cos_lifecycle --action=create --rule-id=my-rule --prefix=temp/ --days=30

# 删除规则
./cos_lifecycle --action=delete --rule-id=my-rule

# 启用/禁用规则
./cos_lifecycle --action=enable --rule-id=my-rule
./cos_lifecycle --action=disable --rule-id=my-rule
```

## 🧪 测试

### 运行测试

```bash
# 运行单元测试
go test ./internal/service -v -run TestCOSLifecycleManager

# 运行基准测试
go test ./internal/service -bench=BenchmarkLifecycleRuleValidation

# 运行所有测试
go test ./internal/service -v
```

### 测试覆盖

- ✅ 配置验证测试
- ✅ 规则验证测试
- ✅ 规则构建测试
- ✅ 集成测试
- ✅ 性能基准测试

## 📝 配置说明

### 必需配置

```yaml
OSS:
  env: "production"                    # 环境标识
  bucket_url: "https://your-bucket.cos.ap-beijing.myqcloud.com"
  secret_id: "your-secret-id"
  secret_key: "your-secret-key"
  face_fusion_ttl_days: 30            # 人脸融合图片TTL（天）
```

### 配置验证

系统会自动验证以下配置：
- `bucket_url` 不能为空且格式正确
- `secret_id` 和 `secret_key` 不能为空
- `env` 环境标识不能为空
- `face_fusion_ttl_days` 为0表示不设置TTL

## 🔧 集成到现有系统

### 1. 在应用启动时设置规则

```go
// 在 cmd/root.go 的 initCron() 或类似位置添加
func initOSSLifecycle() {
    ctx := context.Background()
    uploadService := service.SingletonUploadService()
    
    // 设置人脸融合生命周期规则
    if err := uploadService.SetupCOSLifecycleRules(ctx); err != nil {
        logger.Logger.Errorf("设置COS生命周期规则失败: %v", err)
    }
}
```

### 2. 定期检查规则状态

```go
// 在 cron 任务中添加定期检查
func (c Cron) checkLifecycleRules() {
    c.StartCron("@daily", func() {
        ctx := context.Background()
        uploadService := service.SingletonUploadService()
        
        if err := uploadService.CheckCOSLifecycleRules(ctx); err != nil {
            logger.Logger.Errorf("检查生命周期规则失败: %v", err)
        }
    })
}
```

## 🚨 注意事项

### 1. 权限要求
确保COS账号具有以下权限：
- `PutBucketLifecycle` - 设置生命周期规则
- `GetBucketLifecycle` - 获取生命周期规则
- `DeleteBucketLifecycle` - 删除生命周期规则

### 2. 规则限制
- 每个存储桶最多支持1000个生命周期规则
- 规则ID在存储桶内必须唯一
- 过期天数必须大于0且不超过3650天（10年）

### 3. 生产环境建议
- 在生产环境中谨慎删除规则
- 建议先禁用规则观察一段时间再删除
- 定期检查规则配置的一致性
- 监控规则执行情况和存储成本

## 📊 监控和日志

系统提供详细的日志记录：

```
[OSS生命周期] 开始设置人脸融合生命周期规则，TTL: 30天
[OSS生命周期] 成功设置生命周期规则: 30天后自动删除face_fusion目录下的文件
[OSS生命周期] 找到人脸融合自动删除规则: 状态=Enabled, 前缀=production/face_fusion/, 天数=30
[OSS生命周期] 规则配置不一致: 当前=30天, 期望=7天, 建议更新规则
```

建议在生产环境中监控这些日志，及时发现配置问题。
